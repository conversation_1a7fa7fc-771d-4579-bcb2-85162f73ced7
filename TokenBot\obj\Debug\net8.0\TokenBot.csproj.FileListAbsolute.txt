C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.AssemblyInfo.cs
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\TokenBot.exe
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\TokenBot.deps.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\TokenBot.runtimeconfig.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\TokenBot.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\TokenBot.pdb
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.Http.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Refit.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Refit.HttpClientFactory.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets\msbuild.TokenBot.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets\msbuild.build.TokenBot.props
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.TokenBot.props
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.TokenBot.props
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\scopedcss\bundle\TokenBot.styles.css
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.csproj.Up2Date
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\refint\TokenBot.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.pdb
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\TokenBot.genruntimeconfig.cache
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\obj\Debug\net8.0\ref\TokenBot.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Dapper.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.AspNetCore.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.Console.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.Core.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.NetCore.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.SQLite.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.SqlServer.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\SQLitePCLRaw.core.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\ca\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\de\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\es\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\fa\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\fr\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\nb\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\nl\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\pt-BR\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\pt-PT\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\pt\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\sv\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\tr-TR\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\zh-TW\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\zh\Hangfire.Core.resources.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win7-x64\native\sni.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win7-x86\native\sni.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\unix\lib\netstandard1.3\System.Data.SqlClient.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win\lib\netstandard1.3\System.Data.SqlClient.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\browser-wasm\nativeassets\net8.0\e_sqlite3.a
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-musl-riscv64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-musl-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-riscv64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\runtimes\win-x86\native\e_sqlite3.dll
C:\Users\<USER>\Documents\repos\RLDemo\TokenBot\bin\Debug\net8.0\Hangfire.InMemory.dll
