using Microsoft.AspNetCore.Mvc;
using TokenBot.Models;
using TokenBot.Services;

namespace TokenBot.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DexScreenerController : ControllerBase
{
    private readonly DexScreenerService _dexScreenerService;
    private readonly ILogger<DexScreenerController> _logger;

    public DexScreenerController(DexScreenerService dexScreenerService, ILogger<DexScreenerController> logger)
    {
        _dexScreenerService = dexScreenerService;
        _logger = logger;
    }

    /// <summary>
    /// Get the latest token profiles
    /// </summary>
    [HttpGet("token-profiles/latest")]
    public async Task<ActionResult<List<TokenProfile>>> GetLatestTokenProfiles()
    {
        try
        {
            var profiles = await _dexScreenerService.GetLatestTokenProfilesAsync();
            return Ok(profiles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest token profiles");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get the latest boosted tokens
    /// </summary>
    [HttpGet("token-boosts/latest")]
    public async Task<ActionResult<List<TokenBoost>>> GetLatestTokenBoosts()
    {
        try
        {
            var boosts = await _dexScreenerService.GetLatestTokenBoostsAsync();
            return Ok(boosts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest token boosts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get the tokens with most active boosts
    /// </summary>
    [HttpGet("token-boosts/top")]
    public async Task<ActionResult<List<TokenBoost>>> GetTopTokenBoosts()
    {
        try
        {
            var boosts = await _dexScreenerService.GetTopTokenBoostsAsync();
            return Ok(boosts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top token boosts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Check orders paid for a specific token
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="tokenAddress">Token address</param>
    [HttpGet("orders/{chainId}/{tokenAddress}")]
    public async Task<ActionResult<List<Order>>> GetOrders(string chainId, string tokenAddress)
    {
        try
        {
            var orders = await _dexScreenerService.GetOrdersAsync(chainId, tokenAddress);
            return Ok(orders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting orders for token {TokenAddress} on chain {ChainId}", tokenAddress, chainId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get pair information by chain and pair address
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="pairId">Pair address</param>
    [HttpGet("pairs/{chainId}/{pairId}")]
    public async Task<ActionResult<PairsResponse>> GetPair(string chainId, string pairId)
    {
        try
        {
            var response = await _dexScreenerService.GetPairAsync(chainId, pairId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pair {PairId} on chain {ChainId}", pairId, chainId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Search for pairs matching a query
    /// </summary>
    /// <param name="query">Search query (e.g., "SOL/USDC")</param>
    [HttpGet("search")]
    public async Task<ActionResult<PairsResponse>> SearchPairs([FromQuery] string query)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest("Query parameter is required");
        }

        try
        {
            var response = await _dexScreenerService.SearchPairsAsync(query);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching pairs with query: {Query}", query);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all pairs for a specific token
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="tokenAddress">Token address</param>
    [HttpGet("token-pairs/{chainId}/{tokenAddress}")]
    public async Task<ActionResult<List<Pair>>> GetTokenPairs(string chainId, string tokenAddress)
    {
        try
        {
            var pairs = await _dexScreenerService.GetTokenPairsAsync(chainId, tokenAddress);
            return Ok(pairs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token pairs for {TokenAddress} on chain {ChainId}", tokenAddress, chainId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get pairs by multiple token addresses
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="tokenAddresses">Comma-separated token addresses (up to 30)</param>
    [HttpGet("tokens/{chainId}/{tokenAddresses}")]
    public async Task<ActionResult<List<Pair>>> GetTokens(string chainId, string tokenAddresses)
    {
        if (string.IsNullOrWhiteSpace(tokenAddresses))
        {
            return BadRequest("Token addresses parameter is required");
        }

        var addresses = tokenAddresses.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (addresses.Length > 30)
        {
            return BadRequest("Maximum 30 token addresses allowed");
        }

        try
        {
            var pairs = await _dexScreenerService.GetTokensAsync(chainId, addresses);
            return Ok(pairs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tokens for {Count} addresses on chain {ChainId}", addresses.Length, chainId);
            return StatusCode(500, "Internal server error");
        }
    }
}
