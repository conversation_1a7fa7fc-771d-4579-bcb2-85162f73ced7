# TokenBot - DexScreener API Client with Hangfire Background Jobs

This project implements a Refit-based client for the DexScreener API with Hangfire background job processing, providing easy access to cryptocurrency trading pair data, token information, market analytics, and automated monitoring.

## Features

- **Complete DexScreener API Coverage**: All endpoints from the official API documentation
- **Refit Integration**: Type-safe HTTP client with automatic serialization/deserialization
- **Hangfire Background Jobs**: SQLite-based job processing with web dashboard
- **Automated Token Monitoring**: Background jobs for trending tokens, price alerts, and market analysis
- **Dependency Injection**: Properly configured for ASP.NET Core DI container
- **Logging**: Comprehensive logging for monitoring and debugging
- **Error Handling**: Robust error handling with proper HTTP status codes
- **Rate Limiting Awareness**: Documented rate limits for each endpoint
- **Console Output**: Real-time job progress with Hangfire.Console

## API Endpoints

### DexScreener API Endpoints

The client provides access to all DexScreener API endpoints:

#### Token Information
- `GET /api/dexscreener/token-profiles/latest` - Latest token profiles
- `GET /api/dexscreener/token-boosts/latest` - Latest boosted tokens
- `GET /api/dexscreener/token-boosts/top` - Top boosted tokens

#### Trading Pairs
- `GET /api/dexscreener/pairs/{chainId}/{pairId}` - Get specific pair
- `GET /api/dexscreener/search?query={query}` - Search pairs
- `GET /api/dexscreener/token-pairs/{chainId}/{tokenAddress}` - Get token pairs
- `GET /api/dexscreener/tokens/{chainId}/{tokenAddresses}` - Get multiple tokens

#### Orders
- `GET /api/dexscreener/orders/{chainId}/{tokenAddress}` - Check token orders

### Example Endpoints

The project includes example endpoints demonstrating practical usage:

- `GET /api/example/solana-search?query=SOL` - Search Solana tokens
- `GET /api/example/popular-solana-tokens` - Get popular Solana token info
- `GET /api/example/trending` - Get trending/boosted tokens

### Hangfire Background Jobs

The project includes comprehensive background job functionality:

#### Job Management Endpoints
- `GET /api/jobs/status` - Get job system status and available endpoints
- `POST /api/jobs/test` - Schedule a simple test job
- `POST /api/jobs/setup-recurring` - Set up default recurring jobs
- `DELETE /api/jobs/recurring` - Remove all recurring jobs

#### Token Monitoring Jobs
- `POST /api/jobs/monitor-trending` - Monitor trending/boosted tokens (one-time)
- `POST /api/jobs/monitor-token/{chainId}/{tokenAddress}` - Monitor specific token pairs
- `POST /api/jobs/analyze-patterns?query={searchQuery}` - Analyze token patterns
- `POST /api/jobs/monitor-trending/delayed?delayMinutes={minutes}` - Schedule delayed monitoring

#### Hangfire Dashboard
- `GET /hangfire` - Web-based dashboard for job monitoring and management

## Usage Examples

### Basic Usage

```csharp
// Inject the service in your controller
public class MyController : ControllerBase
{
    private readonly DexScreenerService _dexScreenerService;

    public MyController(DexScreenerService dexScreenerService)
    {
        _dexScreenerService = dexScreenerService;
    }

    // Search for pairs
    public async Task<IActionResult> SearchTokens(string query)
    {
        var results = await _dexScreenerService.SearchPairsAsync(query);
        return Ok(results);
    }
}
```

### Direct API Client Usage

```csharp
// Inject the Refit client directly
public class MyService
{
    private readonly IDexScreenerApi _api;

    public MyService(IDexScreenerApi api)
    {
        _api = api;
    }

    public async Task<List<Pair>> GetTokenPairs(string chainId, string tokenAddress)
    {
        return await _api.GetTokenPairsAsync(chainId, tokenAddress);
    }
}
```

### Background Jobs Usage

```csharp
// Inject Hangfire services in your controller
public class MyController : ControllerBase
{
    private readonly IBackgroundJobClient _backgroundJobClient;

    public MyController(IBackgroundJobClient backgroundJobClient)
    {
        _backgroundJobClient = backgroundJobClient;
    }

    // Schedule a one-time job
    public IActionResult ScheduleMonitoring()
    {
        var jobId = _backgroundJobClient.Enqueue<TokenMonitoringService>(
            service => service.MonitorTrendingTokensAsync(null!));
        return Ok(new { jobId });
    }
}
```

### Recurring Jobs

The application automatically sets up recurring jobs on startup:
- **Daily Market Summary**: Runs daily at 9 AM UTC
- **Trending Monitoring**: Runs every 2 hours

## Configuration

The DexScreener client is configured in `Program.cs`:

```csharp
// Add DexScreener API client
builder.Services.AddRefitClient<IDexScreenerApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri("https://api.dexscreener.com");
        c.DefaultRequestHeaders.Add("User-Agent", "TokenBot/1.0");
    });

// Add DexScreener service
builder.Services.AddScoped<DexScreenerService>();

// Add Hangfire with SQLite storage
builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseSQLiteStorage("Data Source=hangfire.db;")
    .UseConsole());

// Add Hangfire server
builder.Services.AddHangfireServer();
```

### Hangfire Configuration

Hangfire is configured with:
- **SQLite Storage**: Jobs are persisted in `hangfire.db` file
- **Console Extension**: Real-time job progress in the dashboard
- **Automatic Retry**: Failed jobs are automatically retried
- **Web Dashboard**: Available at `/hangfire` endpoint

## Rate Limits

DexScreener API has the following rate limits:
- **Token profiles/boosts/orders**: 60 requests per minute
- **Pairs/search/tokens**: 300 requests per minute

## Data Models

The project includes comprehensive data models for all API responses:

- `TokenProfile` - Token profile information
- `TokenBoost` - Token boost data
- `Pair` - Trading pair information
- `Token` - Basic token data
- `Order` - Order information
- `PairsResponse` - Search/pair response wrapper

## Error Handling

All endpoints include proper error handling:
- Logging of errors with context
- HTTP 500 responses for server errors
- HTTP 400 responses for bad requests
- Detailed error messages in development

## Getting Started

1. **Clone the repository**
2. **Run the application**:
   ```bash
   dotnet run
   ```
3. **Open Swagger UI**: Navigate to `https://localhost:7xxx/swagger`
4. **Test the endpoints**: Use the Swagger interface to test API calls

## Example API Calls

### Search for SOL tokens
```
GET /api/example/solana-search?query=SOL
```

### Get specific pair information
```
GET /api/dexscreener/pairs/solana/JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN
```

### Get token pairs for a specific token
```
GET /api/dexscreener/token-pairs/solana/So11111111111111111111111111111111111111112
```

## Background Jobs

The application includes several types of background jobs:

### Token Monitoring Jobs
- **Trending Tokens Monitoring**: Analyzes latest and top boosted tokens
- **Token Pairs Monitoring**: Monitors specific token pairs for price changes and liquidity
- **Pattern Analysis**: Searches and analyzes token patterns based on queries
- **Daily Market Summary**: Generates comprehensive market reports

### Job Features
- **Real-time Console Output**: See job progress in the Hangfire dashboard
- **Automatic Retries**: Failed jobs are automatically retried up to 3 times
- **Persistent Storage**: Jobs are stored in SQLite database
- **Scheduling Options**: One-time, delayed, and recurring jobs

## Dependencies

- **Refit**: HTTP client library
- **Refit.HttpClientFactory**: Integration with ASP.NET Core DI
- **Hangfire**: Background job processing
- **Hangfire.SQLite**: SQLite storage for Hangfire
- **Hangfire.Console**: Console output for jobs
- **ASP.NET Core**: Web framework
- **System.Text.Json**: JSON serialization

## Contributing

Feel free to submit issues and enhancement requests!
