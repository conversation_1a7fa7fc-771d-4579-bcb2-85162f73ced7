using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using TokenBot.Models;

namespace TokenBot.Services;

/// <summary>
/// Background service for monitoring tokens using DexScreener API
/// </summary>
public class TokenMonitoringService
{
    private readonly DexScreenerService _dexScreenerService;
    private readonly ILogger<TokenMonitoringService> _logger;

    public TokenMonitoringService(DexScreenerService dexScreenerService, ILogger<TokenMonitoringService> logger)
    {
        _dexScreenerService = dexScreenerService;
        _logger = logger;
    }

    /// <summary>
    /// Monitor trending tokens and log significant changes
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task MonitorTrendingTokensAsync(PerformContext context)
    {
        context.WriteLine("🚀 Starting trending tokens monitoring...");

        try
        {
            // Get latest boosted tokens
            context.WriteLine("📈 Fetching latest boosted tokens...");
            var latestBoosts = await _dexScreenerService.GetLatestTokenBoostsAsync();

            context.WriteLine($"✅ Found {latestBoosts.Count} latest boosted tokens");

            // Get top boosted tokens
            context.WriteLine("🔥 Fetching top boosted tokens...");
            var topBoosts = await _dexScreenerService.GetTopTokenBoostsAsync();

            context.WriteLine($"✅ Found {topBoosts.Count} top boosted tokens");

            // Analyze and log interesting findings
            var highValueBoosts = topBoosts
                .Where(b => b.TotalAmount > 1000) // Tokens with significant boost amounts
                .OrderByDescending(b => b.TotalAmount)
                .Take(5)
                .ToList();

            if (highValueBoosts.Any())
            {
                context.WriteLine($"💰 High-value boosts found:");
                foreach (var boost in highValueBoosts)
                {
                    context.WriteLine($"  • {boost.TokenAddress} on {boost.ChainId}: ${boost.TotalAmount:F2}");
                    _logger.LogInformation("High-value boost detected: {TokenAddress} on {ChainId} with ${Amount:F2}",
                        boost.TokenAddress, boost.ChainId, boost.TotalAmount);
                }
            }

            // Check for new trending tokens (latest vs top comparison)
            var newTrendingTokens = latestBoosts
                .Where(latest => !topBoosts.Any(top =>
                    top.TokenAddress == latest.TokenAddress &&
                    top.ChainId == latest.ChainId))
                .Take(3)
                .ToList();

            if (newTrendingTokens.Any())
            {
                context.WriteLine($"🆕 New trending tokens detected:");
                foreach (var token in newTrendingTokens)
                {
                    context.WriteLine($"  • {token.TokenAddress} on {token.ChainId}");
                    _logger.LogInformation("New trending token detected: {TokenAddress} on {ChainId}",
                        token.TokenAddress, token.ChainId);
                }
            }

            context.WriteLine("✅ Trending tokens monitoring completed successfully");
        }
        catch (Exception ex)
        {
            context.WriteLine($"❌ Error monitoring trending tokens: {ex.Message}");
            _logger.LogError(ex, "Error in trending tokens monitoring");
            throw;
        }
    }

    /// <summary>
    /// Monitor specific token pairs for price changes
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task MonitorTokenPairsAsync(string chainId, string tokenAddress, PerformContext context)
    {
        context.WriteLine($"🔍 Monitoring token pairs for {tokenAddress} on {chainId}...");

        try
        {
            var pairs = await _dexScreenerService.GetTokenPairsAsync(chainId, tokenAddress);

            context.WriteLine($"📊 Found {pairs.Count} pairs for token");

            if (!pairs.Any())
            {
                context.WriteLine("⚠️ No pairs found for this token");
                return;
            }

            // Analyze liquidity and volume
            var topPairsByLiquidity = pairs
                .Where(p => p.Liquidity?.Usd.HasValue == true)
                .OrderByDescending(p => p.Liquidity.Usd.Value)
                .Take(3)
                .ToList();

            if (topPairsByLiquidity.Any())
            {
                context.WriteLine("💧 Top pairs by liquidity:");
                foreach (var pair in topPairsByLiquidity)
                {
                    var volume24h = pair.Volume?.ContainsKey("h24") == true ? pair.Volume["h24"] : 0;
                    var priceChange24h = pair.PriceChange?.ContainsKey("h24") == true ? pair.PriceChange["h24"] : 0;

                    context.WriteLine($"  • {pair.PairAddress}: ${pair.Liquidity?.Usd:F2} liquidity, ${volume24h:F2} 24h volume, {priceChange24h:F2}% 24h change");

                    // Alert on significant price changes
                    if (Math.Abs(priceChange24h) > 20)
                    {
                        context.WriteLine($"🚨 ALERT: Significant price change detected: {priceChange24h:F2}%");
                        _logger.LogWarning("Significant price change detected for pair {PairAddress}: {PriceChange:F2}%",
                            pair.PairAddress, priceChange24h);
                    }
                }
            }

            // Check for low liquidity warnings
            var lowLiquidityPairs = pairs
                .Where(p => p.Liquidity?.Usd.HasValue == true && p.Liquidity.Usd.Value < 10000)
                .ToList();

            if (lowLiquidityPairs.Any())
            {
                context.WriteLine($"⚠️ Warning: {lowLiquidityPairs.Count} pairs with low liquidity (<$10k)");
            }

            context.WriteLine("✅ Token pairs monitoring completed");
        }
        catch (Exception ex)
        {
            context.WriteLine($"❌ Error monitoring token pairs: {ex.Message}");
            _logger.LogError(ex, "Error monitoring token pairs for {TokenAddress} on {ChainId}", tokenAddress, chainId);
            throw;
        }
    }

    /// <summary>
    /// Search and analyze specific token patterns
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task AnalyzeTokenPatternsAsync(string searchQuery, PerformContext context)
    {
        context.WriteLine($"🔎 Analyzing token patterns for query: '{searchQuery}'...");

        try
        {
            var searchResults = await _dexScreenerService.SearchPairsAsync(searchQuery);

            if (searchResults?.Pairs == null || !searchResults.Pairs.Any())
            {
                context.WriteLine("❌ No results found for search query");
                return;
            }

            context.WriteLine($"📈 Found {searchResults.Pairs.Count} pairs matching query");

            // Analyze by chain
            var chainAnalysis = searchResults.Pairs
                .GroupBy(p => p.ChainId)
                .Select(g => new
                {
                    ChainId = g.Key,
                    Count = g.Count(),
                    TotalLiquidity = g.Sum(p => p.Liquidity?.Usd ?? 0),
                    AvgPrice = g.Average(p => decimal.TryParse(p.PriceUsd, out var price) ? price : 0)
                })
                .OrderByDescending(x => x.TotalLiquidity)
                .ToList();

            context.WriteLine("🔗 Analysis by blockchain:");
            foreach (var chain in chainAnalysis)
            {
                context.WriteLine($"  • {chain.ChainId}: {chain.Count} pairs, ${chain.TotalLiquidity:F2} total liquidity, ${chain.AvgPrice:F6} avg price");
            }

            // Find pairs with unusual characteristics
            var highVolumePairs = searchResults.Pairs
                .Where(p => p.Volume?.ContainsKey("h24") == true && p.Volume["h24"] > 100000)
                .OrderByDescending(p => p.Volume["h24"])
                .Take(3)
                .ToList();

            if (highVolumePairs.Any())
            {
                context.WriteLine("📊 High volume pairs (>$100k 24h):");
                foreach (var pair in highVolumePairs)
                {
                    var volume = pair.Volume["h24"];
                    context.WriteLine($"  • {pair.BaseToken?.Symbol}/{pair.QuoteToken?.Symbol} on {pair.ChainId}: ${volume:F2}");
                }
            }

            context.WriteLine("✅ Token pattern analysis completed");
        }
        catch (Exception ex)
        {
            context.WriteLine($"❌ Error analyzing token patterns: {ex.Message}");
            _logger.LogError(ex, "Error analyzing token patterns for query: {SearchQuery}", searchQuery);
            throw;
        }
    }

    /// <summary>
    /// Generate daily market summary report
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task GenerateDailyMarketSummaryAsync(PerformContext context)
    {
        context.WriteLine("📊 Generating daily market summary...");

        try
        {
            // Get trending data
            var latestBoosts = await _dexScreenerService.GetLatestTokenBoostsAsync();
            var topBoosts = await _dexScreenerService.GetTopTokenBoostsAsync();
            var profiles = await _dexScreenerService.GetLatestTokenProfilesAsync();

            context.WriteLine($"📈 Market Data Summary:");
            context.WriteLine($"  • Latest Boosts: {latestBoosts.Count}");
            context.WriteLine($"  • Top Boosts: {topBoosts.Count}");
            context.WriteLine($"  • Token Profiles: {profiles.Count}");

            // Analyze boost trends
            var totalBoostAmount = topBoosts.Sum(b => b.TotalAmount);
            var avgBoostAmount = topBoosts.Any() ? topBoosts.Average(b => b.TotalAmount) : 0;

            context.WriteLine($"💰 Boost Analytics:");
            context.WriteLine($"  • Total Boost Amount: ${totalBoostAmount:F2}");
            context.WriteLine($"  • Average Boost Amount: ${avgBoostAmount:F2}");

            // Chain distribution
            var chainDistribution = topBoosts
                .GroupBy(b => b.ChainId)
                .Select(g => new { Chain = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .ToList();

            context.WriteLine($"🔗 Chain Distribution:");
            foreach (var chain in chainDistribution.Take(5))
            {
                context.WriteLine($"  • {chain.Chain}: {chain.Count} boosts");
            }

            // Log summary for persistence
            _logger.LogInformation("Daily Market Summary - Boosts: {LatestCount}/{TopCount}, Profiles: {ProfileCount}, Total Boost Value: ${TotalValue:F2}",
                latestBoosts.Count, topBoosts.Count, profiles.Count, totalBoostAmount);

            context.WriteLine("✅ Daily market summary completed");
        }
        catch (Exception ex)
        {
            context.WriteLine($"❌ Error generating market summary: {ex.Message}");
            _logger.LogError(ex, "Error generating daily market summary");
            throw;
        }
    }
}
