using Hangfire;
using Microsoft.AspNetCore.Mvc;
using TokenBot.Services;

namespace TokenBot.Controllers;

[ApiController]
[Route("api/[controller]")]
public class JobsController : ControllerBase
{
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IRecurringJobManager _recurringJobManager;
    private readonly ILogger<JobsController> _logger;

    public JobsController(
        IBackgroundJobClient backgroundJobClient,
        IRecurringJobManager recurringJobManager,
        ILogger<JobsController> logger)
    {
        _backgroundJobClient = backgroundJobClient;
        _recurringJobManager = recurringJobManager;
        _logger = logger;
    }

    /// <summary>
    /// Schedule a one-time trending tokens monitoring job
    /// </summary>
    [HttpPost("monitor-trending")]
    public IActionResult ScheduleTrendingMonitoring()
    {
        try
        {
            var jobId = _backgroundJobClient.Enqueue<TokenMonitoringService>(
                service => service.MonitorTrendingTokensAsync(null!));

            _logger.LogInformation("Scheduled trending tokens monitoring job: {JobId}", jobId);

            return Ok(new {
                message = "Trending tokens monitoring job scheduled",
                jobId,
                dashboardUrl = "/hangfire/jobs/details/" + jobId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling trending tokens monitoring job");
            return StatusCode(500, new { error = "Failed to schedule job", message = ex.Message });
        }
    }

    /// <summary>
    /// Schedule a one-time token pairs monitoring job for a specific token
    /// </summary>
    [HttpPost("monitor-token/{chainId}/{tokenAddress}")]
    public IActionResult ScheduleTokenMonitoring(string chainId, string tokenAddress)
    {
        try
        {
            var jobId = _backgroundJobClient.Enqueue<TokenMonitoringService>(
                service => service.MonitorTokenPairsAsync(chainId, tokenAddress, null!));

            _logger.LogInformation("Scheduled token monitoring job for {TokenAddress} on {ChainId}: {JobId}",
                tokenAddress, chainId, jobId);

            return Ok(new {
                message = $"Token monitoring job scheduled for {tokenAddress} on {chainId}",
                jobId,
                chainId,
                tokenAddress,
                dashboardUrl = "/hangfire/jobs/details/" + jobId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling token monitoring job for {TokenAddress} on {ChainId}",
                tokenAddress, chainId);
            return StatusCode(500, new { error = "Failed to schedule job", message = ex.Message });
        }
    }

    /// <summary>
    /// Schedule a token pattern analysis job
    /// </summary>
    [HttpPost("analyze-patterns")]
    public IActionResult SchedulePatternAnalysis([FromQuery] string query)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest(new { error = "Query parameter is required" });
        }

        try
        {
            var jobId = _backgroundJobClient.Enqueue<TokenMonitoringService>(
                service => service.AnalyzeTokenPatternsAsync(query, null!));

            _logger.LogInformation("Scheduled pattern analysis job for query '{Query}': {JobId}", query, jobId);

            return Ok(new {
                message = $"Pattern analysis job scheduled for query: {query}",
                jobId,
                query,
                dashboardUrl = "/hangfire/jobs/details/" + jobId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling pattern analysis job for query '{Query}'", query);
            return StatusCode(500, new { error = "Failed to schedule job", message = ex.Message });
        }
    }

    /// <summary>
    /// Schedule a delayed job (runs after specified delay)
    /// </summary>
    [HttpPost("monitor-trending/delayed")]
    public IActionResult ScheduleDelayedTrendingMonitoring([FromQuery] int delayMinutes = 5)
    {
        try
        {
            var jobId = _backgroundJobClient.Schedule<TokenMonitoringService>(
                service => service.MonitorTrendingTokensAsync(null!),
                TimeSpan.FromMinutes(delayMinutes));

            _logger.LogInformation("Scheduled delayed trending monitoring job: {JobId} (delay: {DelayMinutes} minutes)",
                jobId, delayMinutes);

            return Ok(new {
                message = $"Delayed trending monitoring job scheduled (runs in {delayMinutes} minutes)",
                jobId,
                delayMinutes,
                dashboardUrl = "/hangfire/jobs/details/" + jobId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling delayed trending monitoring job");
            return StatusCode(500, new { error = "Failed to schedule delayed job", message = ex.Message });
        }
    }

    /// <summary>
    /// Set up recurring jobs for automated monitoring
    /// </summary>
    [HttpPost("setup-recurring")]
    public IActionResult SetupRecurringJobs()
    {
        try
        {
            // Daily market summary at 9 AM UTC
            _recurringJobManager.AddOrUpdate<TokenMonitoringService>(
                "daily-market-summary",
                service => service.GenerateDailyMarketSummaryAsync(null!),
                "0 9 * * *", // Daily at 9 AM UTC
                TimeZoneInfo.Utc);

            // Trending tokens monitoring every 30 minutes
            _recurringJobManager.AddOrUpdate<TokenMonitoringService>(
                "trending-monitoring",
                service => service.MonitorTrendingTokensAsync(null!),
                "*/30 * * * *", // Every 30 minutes
                TimeZoneInfo.Utc);

            // SOL token monitoring every hour
            _recurringJobManager.AddOrUpdate<TokenMonitoringService>(
                "sol-monitoring",
                service => service.MonitorTokenPairsAsync("solana", "So11111111111111111111111111111111111111112", null!),
                "0 * * * *", // Every hour
                TimeZoneInfo.Utc);

            _logger.LogInformation("Recurring jobs set up successfully");

            return Ok(new {
                message = "Recurring jobs set up successfully",
                jobs = new[]
                {
                    new { id = "daily-market-summary", schedule = "Daily at 9 AM UTC", description = "Generate daily market summary" },
                    new { id = "trending-monitoring", schedule = "Every 30 minutes", description = "Monitor trending tokens" },
                    new { id = "sol-monitoring", schedule = "Every hour", description = "Monitor SOL token pairs" }
                },
                dashboardUrl = "/hangfire/recurring"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up recurring jobs");
            return StatusCode(500, new { error = "Failed to set up recurring jobs", message = ex.Message });
        }
    }

    /// <summary>
    /// Remove all recurring jobs
    /// </summary>
    [HttpDelete("recurring")]
    public IActionResult RemoveRecurringJobs()
    {
        try
        {
            _recurringJobManager.RemoveIfExists("daily-market-summary");
            _recurringJobManager.RemoveIfExists("trending-monitoring");
            _recurringJobManager.RemoveIfExists("sol-monitoring");

            _logger.LogInformation("All recurring jobs removed");

            return Ok(new { message = "All recurring jobs removed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing recurring jobs");
            return StatusCode(500, new { error = "Failed to remove recurring jobs", message = ex.Message });
        }
    }

    /// <summary>
    /// Get job status and information
    /// </summary>
    [HttpGet("status")]
    public IActionResult GetJobsStatus()
    {
        try
        {
            return Ok(new
            {
                message = "Hangfire is running",
                dashboardUrl = "/hangfire",
                endpoints = new
                {
                    monitorTrending = "/api/jobs/monitor-trending",
                    monitorToken = "/api/jobs/monitor-token/{chainId}/{tokenAddress}",
                    analyzePatterns = "/api/jobs/analyze-patterns?query={searchQuery}",
                    setupRecurring = "/api/jobs/setup-recurring",
                    removeRecurring = "/api/jobs/recurring (DELETE)"
                },
                examples = new
                {
                    monitorSolana = "/api/jobs/monitor-token/solana/So11111111111111111111111111111111111111112",
                    analyzeSOL = "/api/jobs/analyze-patterns?query=SOL",
                    analyzeUSDC = "/api/jobs/analyze-patterns?query=USDC"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting jobs status");
            return StatusCode(500, new { error = "Failed to get jobs status", message = ex.Message });
        }
    }

    /// <summary>
    /// Quick test job to verify Hangfire is working
    /// </summary>
    [HttpPost("test")]
    public IActionResult ScheduleTestJob()
    {
        try
        {
            var jobId = _backgroundJobClient.Enqueue(() => TestJob());

            return Ok(new {
                message = "Test job scheduled",
                jobId,
                dashboardUrl = "/hangfire/jobs/details/" + jobId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling test job");
            return StatusCode(500, new { error = "Failed to schedule test job", message = ex.Message });
        }
    }

    /// <summary>
    /// Simple test job method
    /// </summary>
    public void TestJob()
    {
        _logger.LogInformation("🎉 Test job executed successfully at {Timestamp}", DateTime.UtcNow);
        Console.WriteLine($"🎉 Test job executed successfully at {DateTime.UtcNow}");
    }
}
