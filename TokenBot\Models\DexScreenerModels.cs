using System.Text.Json.Serialization;

namespace TokenBot.Models;

// Base models
public class TokenProfile
{
    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonPropertyName("chainId")]
    public string? ChainId { get; set; }

    [JsonPropertyName("tokenAddress")]
    public string? TokenAddress { get; set; }

    [JsonPropertyName("icon")]
    public string? Icon { get; set; }

    [JsonPropertyName("header")]
    public string? Header { get; set; }

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("links")]
    public List<Link>? Links { get; set; }
}

public class TokenBoost
{
    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonPropertyName("chainId")]
    public string? ChainId { get; set; }

    [Json<PERSON>ropertyName("tokenAddress")]
    public string? TokenAddress { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("totalAmount")]
    public decimal TotalAmount { get; set; }

    [JsonPropertyName("icon")]
    public string? Icon { get; set; }

    [JsonPropertyName("header")]
    public string? Header { get; set; }

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("links")]
    public List<Link>? Links { get; set; }
}

public class Link
{
    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("label")]
    public string? Label { get; set; }

    [JsonPropertyName("url")]
    public string? Url { get; set; }
}

public class Order
{
    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("paymentTimestamp")]
    public long PaymentTimestamp { get; set; }
}

public class Token
{
    [JsonPropertyName("address")]
    public string? Address { get; set; }

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("symbol")]
    public string? Symbol { get; set; }
}

public class TransactionData
{
    [JsonPropertyName("buys")]
    public int Buys { get; set; }

    [JsonPropertyName("sells")]
    public int Sells { get; set; }
}

public class Liquidity
{
    [JsonPropertyName("usd")]
    public decimal? Usd { get; set; }

    [JsonPropertyName("base")]
    public decimal? Base { get; set; }

    [JsonPropertyName("quote")]
    public decimal? Quote { get; set; }
}

public class Website
{
    [JsonPropertyName("url")]
    public string? Url { get; set; }
}

public class Social
{
    [JsonPropertyName("platform")]
    public string? Platform { get; set; }

    [JsonPropertyName("handle")]
    public string? Handle { get; set; }
}

public class PairInfo
{
    [JsonPropertyName("imageUrl")]
    public string? ImageUrl { get; set; }

    [JsonPropertyName("websites")]
    public List<Website>? Websites { get; set; }

    [JsonPropertyName("socials")]
    public List<Social>? Socials { get; set; }
}

public class Boosts
{
    [JsonPropertyName("active")]
    public int Active { get; set; }
}

public class Pair
{
    [JsonPropertyName("chainId")]
    public string? ChainId { get; set; }

    [JsonPropertyName("dexId")]
    public string? DexId { get; set; }

    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonPropertyName("pairAddress")]
    public string? PairAddress { get; set; }

    [JsonPropertyName("labels")]
    public List<string>? Labels { get; set; }

    [JsonPropertyName("baseToken")]
    public Token? BaseToken { get; set; }

    [JsonPropertyName("quoteToken")]
    public Token? QuoteToken { get; set; }

    [JsonPropertyName("priceNative")]
    public string? PriceNative { get; set; }

    [JsonPropertyName("priceUsd")]
    public string? PriceUsd { get; set; }

    [JsonPropertyName("txns")]
    public Dictionary<string, TransactionData>? Txns { get; set; }

    [JsonPropertyName("volume")]
    public Dictionary<string, decimal>? Volume { get; set; }

    [JsonPropertyName("priceChange")]
    public Dictionary<string, decimal>? PriceChange { get; set; }

    [JsonPropertyName("liquidity")]
    public Liquidity? Liquidity { get; set; }

    [JsonPropertyName("fdv")]
    public decimal? Fdv { get; set; }

    [JsonPropertyName("marketCap")]
    public decimal? MarketCap { get; set; }

    [JsonPropertyName("pairCreatedAt")]
    public long? PairCreatedAt { get; set; }

    [JsonPropertyName("info")]
    public PairInfo? Info { get; set; }

    [JsonPropertyName("boosts")]
    public Boosts? Boosts { get; set; }
}

// Response wrappers
public class PairsResponse
{
    [JsonPropertyName("schemaVersion")]
    public string? SchemaVersion { get; set; }

    [JsonPropertyName("pairs")]
    public List<Pair>? Pairs { get; set; }
}
