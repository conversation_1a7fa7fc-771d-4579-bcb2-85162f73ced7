@TokenBot_HostAddress = http://localhost:5151

### Example endpoints - Search for Solana tokens
GET {{TokenBot_HostAddress}}/api/example/solana-search?query=SOL
Accept: application/json

###

### Example endpoints - Get popular Solana tokens
GET {{TokenBot_HostAddress}}/api/example/popular-solana-tokens
Accept: application/json

###

### Example endpoints - Get trending tokens
GET {{TokenBot_HostAddress}}/api/example/trending
Accept: application/json

###

### DexScreener API - Search pairs
GET {{TokenBot_HostAddress}}/api/dexscreener/search?query=SOL/USDC
Accept: application/json

###

### DexScreener API - Get latest token profiles
GET {{TokenBot_HostAddress}}/api/dexscreener/token-profiles/latest
Accept: application/json

###

### DexScreener API - Get latest token boosts
GET {{TokenBot_HostAddress}}/api/dexscreener/token-boosts/latest
Accept: application/json

###

### DexScreener API - Get top token boosts
GET {{TokenBot_HostAddress}}/api/dexscreener/token-boosts/top
Accept: application/json

###

### DexScreener API - Get SOL token pairs
GET {{TokenBot_HostAddress}}/api/dexscreener/token-pairs/solana/So11111111111111111111111111111111111111112
Accept: application/json

###

### DexScreener API - Get multiple tokens (SOL, USDC, USDT)
GET {{TokenBot_HostAddress}}/api/dexscreener/tokens/solana/So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB
Accept: application/json

###

### DexScreener API - Get specific pair (Jupiter SOL/USDC)
GET {{TokenBot_HostAddress}}/api/dexscreener/pairs/solana/JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN
Accept: application/json

###

### Hangfire Jobs - Get jobs status
GET {{TokenBot_HostAddress}}/api/jobs/status
Accept: application/json

###

### Hangfire Jobs - Schedule test job
POST {{TokenBot_HostAddress}}/api/jobs/test
Accept: application/json

###

### Hangfire Jobs - Monitor trending tokens (one-time)
POST {{TokenBot_HostAddress}}/api/jobs/monitor-trending
Accept: application/json

###

### Hangfire Jobs - Monitor SOL token pairs
POST {{TokenBot_HostAddress}}/api/jobs/monitor-token/solana/So11111111111111111111111111111111111111112
Accept: application/json

###

### Hangfire Jobs - Analyze SOL patterns
POST {{TokenBot_HostAddress}}/api/jobs/analyze-patterns?query=SOL
Accept: application/json

###

### Hangfire Jobs - Analyze USDC patterns
POST {{TokenBot_HostAddress}}/api/jobs/analyze-patterns?query=USDC
Accept: application/json

###

### Hangfire Jobs - Schedule delayed trending monitoring (5 minutes)
POST {{TokenBot_HostAddress}}/api/jobs/monitor-trending/delayed?delayMinutes=5
Accept: application/json

###

### Hangfire Jobs - Setup recurring jobs
POST {{TokenBot_HostAddress}}/api/jobs/setup-recurring
Accept: application/json

###

### Hangfire Jobs - Remove recurring jobs
DELETE {{TokenBot_HostAddress}}/api/jobs/recurring
Accept: application/json

###
