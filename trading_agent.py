import torch
import random
import numpy as np
import pandas as pd
import os
import glob
from collections import deque
from coin_game import CoinGameAI, Action
from model import Linear_QNet, QTrainer
from helper import plot

# Trading specific parameters
MAX_MEMORY = 100_000
BATCH_SIZE = 1000
LR = 0.001

class TradingAgent:
    def __init__(self):
        self.n_games = 0
        self.epsilon = 0  # randomness
        self.gamma = 0.9  # discount rate
        self.memory = deque(maxlen=MAX_MEMORY)

        # Model: 20 state features -> 256 hidden -> 3 actions (BUY, SELL, HOLD)
        self.model = Linear_QNet(20, 256, 3)
        self.trainer = QTrainer(self.model, lr=LR, gamma=self.gamma)

    def get_state(self, game):
        """Get state from the trading game"""
        return game.get_state()

    def remember(self, state, action, reward, next_state, done):
        """Store experience in memory"""
        self.memory.append((state, action, reward, next_state, done))

    def train_long_memory(self):
        """Train on a batch of experiences"""
        if len(self.memory) > BATCH_SIZE:
            mini_sample = random.sample(self.memory, BATCH_SIZE)
        else:
            mini_sample = self.memory

        states, actions, rewards, next_states, dones = zip(*mini_sample)
        self.trainer.train_step(states, actions, rewards, next_states, dones)

    def train_short_memory(self, state, action, reward, next_state, done):
        """Train on single experience"""
        self.trainer.train_step(state, action, reward, next_state, done)

    def get_action(self, state):
        """Get action using epsilon-greedy policy"""
        # Exploration vs exploitation
        self.epsilon = 100 - self.n_games
        final_move = [0, 0, 0]

        if random.randint(0, 200) < self.epsilon:
            # Random action
            move = random.randint(0, 2)
            final_move[move] = 1
        else:
            # Model prediction
            state0 = torch.tensor(state, dtype=torch.float)
            prediction = self.model(state0)
            move = torch.argmax(prediction).item()
            final_move[move] = 1

        return final_move


def load_all_crypto_data():
    """Load all cryptocurrency data files from data folder"""
    data_folder = 'data'
    dataframes = []

    try:
        # Find all CSV files in data folder
        csv_files = glob.glob(os.path.join(data_folder, '*.csv'))

        if not csv_files:
            print("No CSV files found in data folder, creating sample data...")
            return [create_sample_data()]

        print(f"Found {len(csv_files)} crypto data files")

        for file_path in csv_files:
            try:
                # Load the CSV file
                df = pd.read_csv(file_path)

                # Rename columns to match expected format
                df = df.rename(columns={
                    'o': 'open',
                    'h': 'high',
                    'l': 'low',
                    'c': 'close',
                    'v': 'volume'
                })

                # Select only the OHLCV columns we need
                df = df[['open', 'high', 'low', 'close', 'volume']].copy()

                # Remove any rows with missing data
                df = df.dropna()

                # Only include datasets with sufficient data
                if len(df) >= 100:
                    dataframes.append(df)
                    file_name = os.path.basename(file_path)
                    print(f"Loaded {file_name}: {len(df)} periods, "
                          f"price range: ${df['close'].min():.6f} - ${df['close'].max():.6f}")
                else:
                    print(f"Skipping {os.path.basename(file_path)}: insufficient data ({len(df)} periods)")

            except Exception as e:
                print(f"Error loading {os.path.basename(file_path)}: {e}")
                continue

        if not dataframes:
            print("No valid crypto data found, creating sample data...")
            return [create_sample_data()]

        print(f"Successfully loaded {len(dataframes)} crypto datasets")
        return dataframes

    except Exception as e:
        print(f"Error accessing data folder: {e}, creating sample data...")
        return [create_sample_data()]

def load_crypto_data():
    """Load single cryptocurrency data from CSV file (for backward compatibility)"""
    all_data = load_all_crypto_data()
    return all_data[0] if all_data else create_sample_data()

def create_sample_data():
    """Create sample cryptocurrency data for testing (fallback)"""
    np.random.seed(42)
    n_periods = 2000

    # Generate realistic crypto price movement with trends
    base_trend = 0.0001  # Slight upward trend
    volatility = 0.03

    returns = []
    for i in range(n_periods):
        # Add some cyclical patterns
        cycle = 0.001 * np.sin(i / 50)  # 50-period cycle
        noise = np.random.normal(0, volatility)
        returns.append(base_trend + cycle + noise)

    # Generate prices starting from $100
    prices = [100.0]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))

    # Create OHLCV data
    data = []
    for i in range(len(prices)-1):
        open_price = prices[i]
        close_price = prices[i+1]

        # Generate realistic high/low
        high_factor = 1 + abs(np.random.normal(0, 0.005))
        low_factor = 1 - abs(np.random.normal(0, 0.005))

        high = max(open_price, close_price) * high_factor
        low = min(open_price, close_price) * low_factor
        volume = np.random.randint(10000, 100000)

        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })

    return pd.DataFrame(data)


def load_eval_data():
    """Load all evaluation data files from evalData folder"""
    eval_folder = 'evalData'
    dataframes = []

    try:
        # Find all CSV files in evalData folder
        csv_files = glob.glob(os.path.join(eval_folder, '*.csv'))

        if not csv_files:
            print("No evaluation data found in evalData folder")
            return []

        print(f"Found {len(csv_files)} evaluation data files")

        for file_path in csv_files:
            try:
                # Load the CSV file
                df = pd.read_csv(file_path)

                # Rename columns to match expected format
                df = df.rename(columns={
                    'o': 'open',
                    'h': 'high',
                    'l': 'low',
                    'c': 'close',
                    'v': 'volume'
                })

                # Select only the OHLCV columns we need
                df = df[['open', 'high', 'low', 'close', 'volume']].copy()

                # Remove any rows with missing data
                df = df.dropna()

                # Only include datasets with sufficient data
                if len(df) >= 100:
                    dataframes.append(df)
                else:
                    print(f"Skipping {os.path.basename(file_path)}: insufficient data ({len(df)} periods)")

            except Exception as e:
                print(f"Error loading {os.path.basename(file_path)}: {e}")
                continue

        print(f"Successfully loaded {len(dataframes)} evaluation datasets")
        return dataframes

    except Exception as e:
        print(f"Error accessing evalData folder: {e}")
        return []


def evaluate_model_performance(agent):
    """Evaluate model performance on all evaluation datasets"""
    eval_dataframes = load_eval_data()
  #  eval_dataframes = [df.iloc[len(df) // 2:] for df in eval_dataframes]

    if not eval_dataframes:
        print("No evaluation data available, skipping evaluation")
        return 0.0

    total_performance = 0.0
    successful_evaluations = 0

    print(f"Evaluating model on {len(eval_dataframes)} datasets...")

    for i, eval_df in enumerate(eval_dataframes):
        try:
            # Create game with evaluation data
            game = CoinGameAI(eval_df)

            # Run episode with current model (no exploration)
            while True:
                state = agent.get_state(game)

                # Use current model for prediction (no exploration)
                state_tensor = torch.tensor(state, dtype=torch.float)
                with torch.no_grad():
                    prediction = agent.model(state_tensor)
                    action_idx = torch.argmax(prediction).item()

                final_move = [0, 0, 0]
                final_move[action_idx] = 1

                reward, done, profit = game.play_step(final_move)

                if done:
                    final_balance = game._get_absolute_balance()

                    # Zabezpieczenie przed błędnymi danymi również w ewaluacji
                    max_realistic_return = 100.0  # 100x wkład
                    if final_balance > max_realistic_return:
                        print(f"ODRZUCONO w ewaluacji: Nierealistyczny zysk {final_balance:.2f}x")
                        break  # Pomijamy ten dataset w ewaluacji

                    total_performance += final_balance
                    successful_evaluations += 1
                    break

        except Exception as e:
            print(f"Error evaluating on dataset {i+1}: {e}")
            continue

    if successful_evaluations == 0:
        print("No successful evaluations")
        return 0.0

    average_performance = total_performance / successful_evaluations
    print(f"Average performance on {successful_evaluations} datasets: ${average_performance:.4f}")

    return average_performance


def train_trading_agent():
    """Train the trading agent"""
    print("Loading cryptocurrency data...")
    all_dataframes = load_all_crypto_data()

  #  all_dataframes = [df.iloc[len(df) // 2:] for df in all_dataframes]


    plot_scores = []
    plot_mean_scores = []
    total_score = 0
    record = -float('inf')  # Can have negative profits
    best_eval_performance = 0.0  # Best evaluation performance
    agent = TradingAgent()

    dataset_switch_interval = 1  # Switch dataset every episode
    eval_interval = 10  # Evaluate model every 10 episodes

    # Zabezpieczenia przed zapętleniem
    dataset_rejection_count = {}  # Licznik odrzuceń dla każdego datasetu
    max_rejections_per_dataset = 5  # Maksymalna liczba odrzuceń z rzędu
    blacklisted_datasets = set()  # Tymczasowo wykluczone datasety
    consecutive_rejections = 0  # Licznik kolejnych odrzuceń
    max_consecutive_rejections = 20  # Maksymalna liczba kolejnych odrzuceń

    print("Starting training...")

    while agent.n_games < 2000:  # Train for 2000 episodes
        # Wybierz dataset - pomijaj blacklistowane
        available_datasets = [i for i in range(len(all_dataframes)) if i not in blacklisted_datasets]

        if not available_datasets:
            print("Wszystkie datasety zostały tymczasowo wykluczone! Resetowanie blacklisty...")
            blacklisted_datasets.clear()
            dataset_rejection_count.clear()
            available_datasets = list(range(len(all_dataframes)))

        dataset_idx = available_datasets[(agent.n_games // dataset_switch_interval) % len(available_datasets)]
        current_df = all_dataframes[dataset_idx]

        if agent.n_games % dataset_switch_interval == 0:
            print(f"Switching to dataset {dataset_idx + 1}/{len(all_dataframes)} "
                  f"({len(current_df)} periods)")

        # Create new game instance for each episode
        game = CoinGameAI(current_df)
        episode_reward = 0

        while True:
            # Get current state
            state_old = agent.get_state(game)

            # Get action
            final_move = agent.get_action(state_old)

            # Perform move
            reward, done, profit = game.play_step(final_move)
            state_new = agent.get_state(game)

            episode_reward += reward

            # Train short memory
            agent.train_short_memory(state_old, final_move, reward, state_new, done)

            # Remember
            agent.remember(state_old, final_move, reward, state_new, done)

            if done:
                # Episode finished
                final_absolute_balance = game._get_absolute_balance()
                final_return_pct = (final_absolute_balance - 1.0) * 100

                # Zabezpieczenie przed błędnymi danymi - odrzuć gry z nierealistycznymi zyskami
                max_realistic_return = 100.0  # 100x wkład (10000% zysku)
                if final_absolute_balance > max_realistic_return:
                    print(f"ODRZUCONO: Nierealistyczny zysk {final_absolute_balance:.2f}x "
                          f"({final_return_pct:.1f}%) - prawdopodobnie błędne dane")
                    game.reset()
                    # Nie zwiększamy n_games, nie trenujemy - po prostu pomijamy tę grę
                    break

                game.reset()
                agent.n_games += 1
                agent.train_long_memory()

                # Track performance
                if final_absolute_balance > record:
                    record = final_absolute_balance

                # Evaluate model on evaluation data every eval_interval episodes
                if agent.n_games % eval_interval == 0:
                    print(f"\n--- Evaluating model at episode {agent.n_games} ---")
                    current_eval_performance = evaluate_model_performance(agent)

                    if current_eval_performance > best_eval_performance:
                        best_eval_performance = current_eval_performance
                        agent.model.save('trading_model.pth')
                        print(f"New best model saved! Eval performance: ${current_eval_performance:.4f}")
                    else:
                        print(f"Model not improved. Current: ${current_eval_performance:.4f}, "
                              f"Best: ${best_eval_performance:.4f}")
                    print("--- Evaluation complete ---\n")

                print(f'Game {agent.n_games}, Final Value: ${final_absolute_balance:.4f} '
                      f'({final_return_pct:+.2f}%), Episode Reward: {episode_reward:.2f}, '
                      f'Record: ${record:.4f}, Best Eval: ${best_eval_performance:.4f}')

                plot_scores.append(final_absolute_balance)
                total_score += final_absolute_balance
                mean_score = total_score / agent.n_games
                plot_mean_scores.append(mean_score)
                plot(plot_scores, plot_mean_scores)

                break


def test_trained_agent():
    """Test the trained agent"""
    print("Testing trained agent...")

    # Load trained model
    agent = TradingAgent()
    try:
        agent.model.load_state_dict(torch.load('./model/trading_model.pth'))
        print("Loaded trained model")
    except:
        print("No trained model found, using random agent")

    # Load test data
    df = load_crypto_data()
    game = CoinGameAI(df)

    trades_made = 0

    print("Running test episode...")

    while True:
        state = agent.get_state(game)

        # Use trained policy (no exploration)
        state_tensor = torch.tensor(state, dtype=torch.float)
        with torch.no_grad():
            prediction = agent.model(state_tensor)
            action_idx = torch.argmax(prediction).item()

        final_move = [0, 0, 0]
        final_move[action_idx] = 1

        reward, done, profit = game.play_step(final_move, update_ui=True)

        # Count trades
        if action_idx in [0, 1]:  # BUY or SELL
            trades_made += 1

        if done:
            break

    final_absolute_balance = game._get_absolute_balance()
    final_return_pct = (final_absolute_balance - 1.0) * 100

    print(f"\nTest Results:")
    print(f"Final Value: ${final_absolute_balance:.4f}")
    print(f"Total Return: {final_return_pct:+.2f}%")
    print(f"Total Trades Made: {trades_made}")
    print(f"Number of Buy Signals: {len(game.buy_points)}")
    print(f"Number of Sell Signals: {len(game.sell_points)}")

    # Print trade history and fees
    if game.trades:
        print(f"\nTrade History:")
        total_fees = 0
        for trade in game.trades[-10:]:  # Show last 10 trades
            fee = trade.get('fee', 0)
            total_fees += fee
            if 'fee' in trade:
                print(f"Step {trade['step']}: {trade['action']} at ${trade['price']:.6f}, Fee: ${fee:.4f}")
            else:
                print(f"Step {trade['step']}: {trade['action']} at ${trade['price']:.6f}")

        print(f"\nTotal fees paid: ${sum(trade.get('fee', 0) for trade in game.trades):.4f}")
        print(f"Fee structure: {game.fee_percentage*100:.1f}% + ${game.fee_fixed:.3f}")


if __name__ == '__main__':
    print("=== Cryptocurrency Trading AI ===")
    print("1. Train agent")
    print("2. Test trained agent")
    print("3. Create sample data and run manual test")

    choice = input("Choose option (1/2/3): ")

    if choice == "1":
        train_trading_agent()
    elif choice == "2":
        test_trained_agent()
    elif choice == "3":
        # Manual test with real data
        df = load_crypto_data()
        game = CoinGameAI(df)

        print("Manual test - press keys:")
        print("1 - BUY, 2 - SELL, 3 - HOLD, q - quit")

        while True:
            state = game.get_state()
            print(f"Step: {game.current_step}, Price: ${game.get_current_price():.4f}, "
                  f"Position: {game.position.name}, Balance: ${game.balance:.4f}")

            action_input = input("Action (1/2/3/q): ")

            if action_input == 'q':
                break

            try:
                action_idx = int(action_input) - 1
                if action_idx in [0, 1, 2]:
                    final_move = [0, 0, 0]
                    final_move[action_idx] = 1

                    reward, done, profit = game.play_step(final_move)
                    print(f"Reward: {reward:.2f}, Profit: ${profit:.4f}")

                    if done:
                        print(f"Game finished! Final profit: ${profit:.4f}")
                        break
            except:
                print("Invalid input")
    else:
        print("Invalid choice!")
