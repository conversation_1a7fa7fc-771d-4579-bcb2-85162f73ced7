using Hangfire;
using Hangfire.Console;
using Hangfire.InMemory;
using Refit;
using TokenBot.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add DexScreener API client
builder.Services.AddRefitClient<IDexScreenerApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri("https://api.dexscreener.com");
        c.DefaultRequestHeaders.Add("User-Agent", "TokenBot/1.0");
    });

// Add DexScreener service
builder.Services.AddScoped<DexScreenerService>();

// Add background job services
builder.Services.AddScoped<TokenMonitoringService>();

// Add Hangfire with in-memory storage (for demo purposes)
builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseInMemoryStorage()
    .UseConsole());

// Add Hangfire server
builder.Services.AddHangfireServer();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Add Hangfire Dashboard
app.UseHangfireDashboard("/hangfire");

app.MapControllers();

// Setup default recurring jobs on startup
using (var scope = app.Services.CreateScope())
{
    var recurringJobManager = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();

    // Daily market summary at 9 AM UTC
    recurringJobManager.AddOrUpdate<TokenMonitoringService>(
        "daily-market-summary",
        service => service.GenerateDailyMarketSummaryAsync(null!),
        "0 9 * * *", // Daily at 9 AM UTC
        TimeZoneInfo.Utc);

    // Trending tokens monitoring every 2 hours (less frequent for demo)
    recurringJobManager.AddOrUpdate<TokenMonitoringService>(
        "trending-monitoring",
        service => service.MonitorTrendingTokensAsync(null!),
        "0 */2 * * *", // Every 2 hours
        TimeZoneInfo.Utc);
}

app.Run();
