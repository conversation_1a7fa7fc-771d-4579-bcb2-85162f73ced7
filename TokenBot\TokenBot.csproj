<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="Hangfire.Console" Version="1.4.3" />
    <PackageReference Include="Hangfire.InMemory" Version="1.0.0" />
    <PackageReference Include="Hangfire.SQLite" Version="1.4.2" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.11" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

</Project>
