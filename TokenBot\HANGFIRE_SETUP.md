# Hangfire Integration with DexScreener API

This document describes the Hangfire background job processing integration added to the TokenBot project.

## 🎯 What Was Added

### 1. **Hangfire Packages**
- `Hangfire` - Core background job processing
- `Hangfire.InMemory` - In-memory storage for jobs (demo purposes)
- `Hangfire.Console` - Real-time console output in dashboard

### 2. **Background Job Services**
- `TokenMonitoringService` - Comprehensive token monitoring and analysis

### 3. **Job Management API**
- `JobsController` - REST API for scheduling and managing jobs

### 4. **Hangfire Dashboard**
- Web-based interface at `/hangfire` for monitoring jobs

## 🚀 Available Background Jobs

### Token Monitoring Jobs

#### 1. **Monitor Trending Tokens**
```http
POST /api/jobs/monitor-trending
```
- Fetches latest and top boosted tokens
- Analyzes high-value boosts (>$1000)
- Detects new trending tokens
- Provides real-time console output

#### 2. **Monitor Token Pairs**
```http
POST /api/jobs/monitor-token/{chainId}/{tokenAddress}
```
- Monitors specific token pairs for price changes
- Analyzes liquidity and volume
- Alerts on significant price changes (>20%)
- Warns about low liquidity pairs

#### 3. **Analyze Token Patterns**
```http
POST /api/jobs/analyze-patterns?query={searchQuery}
```
- Searches and analyzes token patterns
- Groups results by blockchain
- Identifies high-volume pairs
- Provides market insights

#### 4. **Daily Market Summary**
```http
POST /api/jobs/setup-recurring
```
- Generates comprehensive market reports
- Analyzes boost trends and chain distribution
- Runs automatically daily at 9 AM UTC

## 🔄 Recurring Jobs

The application automatically sets up these recurring jobs:

1. **Daily Market Summary** - Every day at 9 AM UTC
2. **Trending Monitoring** - Every 2 hours

## 📊 Hangfire Dashboard Features

Access the dashboard at: `http://localhost:5151/hangfire`

### Dashboard Sections:
- **Jobs** - View all job executions (succeeded, failed, processing)
- **Recurring Jobs** - Manage scheduled recurring jobs
- **Servers** - Monitor Hangfire server status
- **Retries** - View and manage failed job retries

### Real-time Features:
- ✅ Live job progress with console output
- ✅ Job execution statistics
- ✅ Automatic retry on failures (up to 3 attempts)
- ✅ Job history and logs

## 🛠️ API Endpoints

### Job Management
```http
GET /api/jobs/status                    # Get system status
POST /api/jobs/test                     # Schedule test job
POST /api/jobs/setup-recurring          # Setup recurring jobs
DELETE /api/jobs/recurring              # Remove recurring jobs
```

### Token Monitoring
```http
POST /api/jobs/monitor-trending         # Monitor trending tokens
POST /api/jobs/monitor-token/solana/So11111111111111111111111111111111111111112
POST /api/jobs/analyze-patterns?query=SOL
POST /api/jobs/monitor-trending/delayed?delayMinutes=5
```

## 💡 Usage Examples

### 1. Schedule Immediate Trending Analysis
```bash
curl -X POST http://localhost:5151/api/jobs/monitor-trending
```

### 2. Monitor SOL Token
```bash
curl -X POST http://localhost:5151/api/jobs/monitor-token/solana/So11111111111111111111111111111111111111112
```

### 3. Analyze USDC Patterns
```bash
curl -X POST "http://localhost:5151/api/jobs/analyze-patterns?query=USDC"
```

### 4. Setup Automated Monitoring
```bash
curl -X POST http://localhost:5151/api/jobs/setup-recurring
```

## 🔧 Configuration

### Hangfire Setup (Program.cs)
```csharp
// Add Hangfire with in-memory storage
builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseInMemoryStorage()
    .UseConsole());

// Add Hangfire server
builder.Services.AddHangfireServer();
```

### Dashboard Access
```csharp
// Add Hangfire Dashboard
app.UseHangfireDashboard("/hangfire");
```

## 📈 Job Features

### Automatic Retry
All jobs are configured with automatic retry (up to 3 attempts):
```csharp
[AutomaticRetry(Attempts = 3)]
public async Task MonitorTrendingTokensAsync(PerformContext context)
```

### Real-time Console Output
Jobs provide real-time progress updates:
```csharp
context.WriteLine("🚀 Starting trending tokens monitoring...");
context.WriteLine($"✅ Found {latestBoosts.Count} latest boosted tokens");
```

### Comprehensive Logging
All jobs include structured logging:
```csharp
_logger.LogInformation("High-value boost detected: {TokenAddress} on {ChainId} with ${Amount:F2}", 
    boost.TokenAddress, boost.ChainId, boost.TotalAmount);
```

## 🎯 Benefits

1. **Automated Monitoring** - Continuous token and market analysis
2. **Real-time Insights** - Live console output and progress tracking
3. **Reliable Processing** - Automatic retries and error handling
4. **Scalable Architecture** - Easy to add new job types
5. **Web Dashboard** - Visual job management and monitoring
6. **Rate Limit Friendly** - Respects DexScreener API rate limits

## 🔍 Monitoring & Debugging

### View Job Details
1. Open `/hangfire` dashboard
2. Navigate to "Jobs" section
3. Click on any job to see detailed execution log
4. View real-time console output and progress

### Check Job Status
```http
GET /api/jobs/status
```

### Test System
```http
POST /api/jobs/test
```

## 🚀 Next Steps

1. **Add More Job Types** - Create additional monitoring jobs
2. **Persistent Storage** - Switch to SQLite or SQL Server for production
3. **Notifications** - Add email/webhook notifications for alerts
4. **Custom Dashboards** - Create custom monitoring dashboards
5. **Job Scheduling** - Add more sophisticated scheduling options

The Hangfire integration provides a robust foundation for automated cryptocurrency market monitoring and analysis!
